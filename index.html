<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kyrgyz LLC Email Sender</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #525F7F;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #8898aa;
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #525F7F;
        }

        input[type="email"], input[type="number"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e6ebf1;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input[type="email"]:focus, input[type="number"]:focus {
            outline: none;
            border-color: #667eea;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 18px 40px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status {
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e6ebf1;
            border-radius: 4px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #525F7F;
        }

        .stat-label {
            color: #8898aa;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .info-box h3 {
            color: #525F7F;
            margin-bottom: 10px;
        }

        .info-box ul {
            color: #8898aa;
            padding-left: 20px;
        }

        .info-box li {
            margin-bottom: 5px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .stats {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📧 Kyrgyz LLC</div>
        <div class="subtitle">Professional Email Receipt Sender</div>
        
        <form id="emailForm">
            <div class="form-group">
                <label for="targetEmail">Target Email Address:</label>
                <input type="email" id="targetEmail" name="targetEmail" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="emailCount">Number of Emails to Send:</label>
                <input type="number" id="emailCount" name="emailCount" value="50" min="1" max="100" required>
            </div>
            
            <button type="submit" class="send-button" id="sendButton">
                🚀 Send Emails
            </button>
        </form>

        <div class="progress-bar" id="progressBar">
            <div class="progress-fill" id="progressFill"></div>
        </div>

        <div class="status" id="status"></div>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="successCount">0</div>
                <div class="stat-label">Successful</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedCount">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCount">0</div>
                <div class="stat-label">Total Sent</div>
            </div>
        </div>

        <div class="info-box">
            <h3>🔒 Features</h3>
            <ul>
                <li><strong>Unique Data Generation:</strong> Each email contains unique Kyrgyz names, receipt numbers, and dates</li>
                <li><strong>Duplicate Prevention:</strong> System tracks all sent data to prevent duplicates</li>
                <li><strong>Professional Templates:</strong> Beautiful HTML email receipts with Stripe-like design</li>
                <li><strong>Data Persistence:</strong> All sent email data is saved to JSON file for future reference</li>
                <li><strong>Real-time Progress:</strong> Live updates during batch sending process</li>
            </ul>
        </div>
    </div>

    <script>
        const form = document.getElementById('emailForm');
        const sendButton = document.getElementById('sendButton');
        const status = document.getElementById('status');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');
        const stats = document.getElementById('stats');
        const successCount = document.getElementById('successCount');
        const failedCount = document.getElementById('failedCount');
        const totalCount = document.getElementById('totalCount');

        function showStatus(message, type) {
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
        }

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            progressFill.style.width = `${percentage}%`;
        }

        function updateStats(results) {
            successCount.textContent = results.success || 0;
            failedCount.textContent = results.failed || 0;
            totalCount.textContent = (results.success || 0) + (results.failed || 0);
            stats.style.display = 'grid';
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const targetEmail = document.getElementById('targetEmail').value;
            const emailCount = parseInt(document.getElementById('emailCount').value);
            
            // Disable form
            sendButton.disabled = true;
            sendButton.textContent = '⏳ Sending Emails...';
            
            // Show progress bar
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';
            
            // Hide previous results
            status.style.display = 'none';
            stats.style.display = 'none';
            
            showStatus(`Starting to send ${emailCount} emails to ${targetEmail}...`, 'info');
            
            try {
                const response = await fetch('/send-emails', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        targetEmail: targetEmail,
                        count: emailCount
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    updateProgress(emailCount, emailCount);
                    showStatus(`✅ Successfully completed! ${data.results.success} emails sent, ${data.results.failed} failed.`, 'success');
                    updateStats(data.results);
                    
                    if (data.results.errors && data.results.errors.length > 0) {
                        console.log('Errors:', data.results.errors);
                    }
                } else {
                    showStatus(`❌ Error: ${data.message}`, 'error');
                }
                
            } catch (error) {
                console.error('Error:', error);
                showStatus(`❌ Network error: ${error.message}`, 'error');
            } finally {
                // Re-enable form
                sendButton.disabled = false;
                sendButton.textContent = '🚀 Send Emails';
                progressBar.style.display = 'none';
            }
        });
    </script>
</body>
</html>
