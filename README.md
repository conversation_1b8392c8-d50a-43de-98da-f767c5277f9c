# 📧 Kyrgyz LLC Email Sender

A professional email receipt sender that generates unique Kyrgyz-style customer data and sends beautiful HTML email receipts. Perfect for testing email systems with realistic, non-repeating data.

## ✨ Features

- **🎯 Batch Email Sending**: Send up to 100 emails at once
- **🔄 Unique Data Generation**: Each email contains unique:
  - Kyrgyz first and last names (48+ first names, 36+ last names)
  - Email addresses with random digits (0-4 digits after name)
  - Receipt numbers (format: XXXX-XXXX)
  - Formatted dates and times
  - Phone numbers with Kyrgyz area codes
  - Visa card numbers (random 4-digit endings)
- **🚫 Duplicate Prevention**: JSON-based tracking prevents duplicate data
- **💎 Professional Templates**: Stripe-inspired HTML email design
- **🌐 Web Interface**: Beautiful, responsive HTML interface
- **📊 Real-time Progress**: Live updates during batch sending
- **💾 Data Persistence**: All sent data saved to `sent_emails.json`

## 🚀 Quick Start

### 1. Setup Environment Variables

Copy the example environment file and configure your SMTP settings:

```bash
cp .env.example .env
```

Edit `.env` with your email provider settings:

```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Server

```bash
npm start
```

### 4. Open the Web Interface

Open your browser and go to: `http://localhost:3000`

## 📧 Email Provider Setup

### Gmail

1. Enable 2-factor authentication
2. Generate an "App Password" for this application
3. Use the app password as `EMAIL_PASSWORD`

### Other Providers

- **Outlook/Hotmail**: `smtp-mail.outlook.com`, port 587
- **Yahoo**: `smtp.mail.yahoo.com`, port 587
- **Custom SMTP**: Use your provider's settings

## 🎮 Usage

1. **Open the web interface** at `http://localhost:3000`
2. **Enter target email** (defaults to `<EMAIL>`)
3. **Set number of emails** (1-100, defaults to 50)
4. **Click "Send Emails"** and watch the progress
5. **View results** with success/failure statistics

## 📁 File Structure

```
email-sign/
├── index.js           # Main server and email logic
├── index.html         # Web interface
├── package.json       # Dependencies and scripts
├── .env.example       # Environment variables template
├── .env              # Your actual environment variables (create this)
├── sent_emails.json  # Generated data tracking (auto-created)
└── README.md         # This file
```

## 🔧 API Endpoints

### POST `/send-emails`

Send a batch of emails with unique data.

**Request Body:**

```json
{
  "targetEmail": "<EMAIL>",
  "count": 50
}
```

**Response:**

```json
{
  "success": true,
  "message": "Batch sending completed",
  "results": {
    "success": 48,
    "failed": 2,
    "errors": ["Email 15 failed to send", "Email 32 error: Connection timeout"]
  }
}
```

## 📊 Data Tracking

The system automatically tracks all generated data in `sent_emails.json`:

```json
{
  "emails": ["<EMAIL>", "<EMAIL>", "..."],
  "receiptNumbers": ["1234-5678", "9876-5432", "..."],
  "dates": ["Jul 25, 2025, 2:30:45 PM", "Jul 26, 2025, 4:15:22 PM", "..."],
  "visaNumbers": ["6274", "8391", "..."]
}
```

This ensures no duplicate data is ever generated across multiple runs.

## 🎨 Customization

### Adding More Names

Edit the `kyrgyzFirstNames` and `kyrgyzLastNames` arrays in `index.js` to add more variety.

### Modifying Email Template

The HTML email template is embedded in the `sendSingleEmail` function. Customize the styling and content as needed.

### Changing Date Range

Modify the date generation logic in `generateUniqueDate` function to use different date ranges.

## 🛠️ Troubleshooting

### Common Issues

1. **"Authentication failed"**: Check your email credentials and app password
2. **"Port already in use"**: Change the PORT variable or kill existing processes
3. **"Unable to generate unique data"**: Clear `sent_emails.json` or increase the generation range

### Debug Mode

Check the console output for detailed logging during email sending process.

## 📝 License

This project is for educational and testing purposes. Please ensure compliance with email sending regulations and your email provider's terms of service.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
