const nodemailer = require("nodemailer");
const fs = require("fs");
const path = require("path");
const express = require("express");
require("dotenv").config();

const app = express();
const PORT = 3000;

// Serve static files
app.use(express.static("."));
app.use(express.json());

// Data file path
const DATA_FILE = path.join(__dirname, "sent_emails.json");

// Load existing data
function loadSentEmails() {
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, "utf8");
      return JSON.parse(data);
    }
  } catch (error) {
    console.error("Error loading sent emails data:", error);
  }
  return { emails: [], receiptNumbers: [], dates: [], visaNumbers: [] };
}

// Save data
function saveSentEmails(data) {
  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify(data, null, 2));
  } catch (error) {
    console.error("Error saving sent emails data:", error);
  }
}

// Expanded Kyrgyz names database
const kyrgyzFirstNames = [
  "Aktan",
  "Azat",
  "Nurlan",
  "Aibek",
  "Eldiyar",
  "Gulnara",
  "Aizada",
  "Bakyt",
  "Bermet",
  "Cholpon",
  "Dastan",
  "Erkin",
  "Farida",
  "Gulmira",
  "Jyldyz",
  "Kanykei",
  "Manas",
  "Nurbek",
  "Omurbek",
  "Perizat",
  "Ruslan",
  "Saltanat",
  "Talant",
  "Ulan",
  "Venera",
  "Zhypar",
  "Ainura",
  "Bektur",
  "Chynara",
  "Dinara",
  "Emil",
  "Fatima",
  "Gulzat",
  "Janyl",
  "Kubat",
  "Maksat",
  "Nazira",
  "Oksana",
  "Roza",
  "Saikal",
  "Tolgonai",
  "Urmat",
  "Zhamilya",
  "Asel",
  "Baktygul",
  "Cholponbek",
  "Erlan",
  "Gulnaz",
];

const kyrgyzLastNames = [
  "Kulabaev",
  "Ismailov",
  "Sydykov",
  "Abdyldaev",
  "Niyazov",
  "Toktosunov",
  "Mamytov",
  "Osmonov",
  "Kasymov",
  "Turgunbaev",
  "Alybaev",
  "Bekmuratov",
  "Choroev",
  "Djumabaev",
  "Ergeshov",
  "Frunzeev",
  "Gulmamatov",
  "Isaev",
  "Jumagulov",
  "Kadyrov",
  "Mamatov",
  "Nazarov",
  "Orozov",
  "Ryskulov",
  "Satybaldiev",
  "Toktomushev",
  "Usenov",
  "Zhusupov",
  "Aitmatov",
  "Bakirov",
  "Chyngyshev",
  "Duisheev",
  "Erkinbekov",
  "Fayzieva",
  "Gapurov",
  "Ibraimov",
];

async function generateUniqueEmail(sentData) {
  let attempts = 0;
  const maxAttempts = 1000;

  while (attempts < maxAttempts) {
    const randomFirstName =
      kyrgyzFirstNames[Math.floor(Math.random() * kyrgyzFirstNames.length)];
    const randomLastName =
      kyrgyzLastNames[Math.floor(Math.random() * kyrgyzLastNames.length)];

    // Generate random number of digits (0-4)
    const numDigits = Math.floor(Math.random() * 5); // 0, 1, 2, 3, or 4 digits
    let randomNumber = "";

    if (numDigits > 0) {
      // Generate random number with specified digits
      const min = Math.pow(10, numDigits - 1);
      const max = Math.pow(10, numDigits) - 1;
      randomNumber = Math.floor(
        Math.random() * (max - min + 1) + min
      ).toString();
    }

    const randomEmail = `${randomFirstName.toLowerCase()}${randomLastName.toLowerCase()}${randomNumber}@gmail.com`;

    // Check if email already exists
    if (!sentData.emails.includes(randomEmail)) {
      return randomEmail;
    }
    attempts++;
  }

  // If we can't generate unique email after max attempts, throw error
  throw new Error("Unable to generate unique email after maximum attempts");
}

async function generateUniqueReceiptNumber(sentData) {
  let attempts = 0;
  const maxAttempts = 1000;

  while (attempts < maxAttempts) {
    const randomReceiptNumber = `${Math.floor(
      1000 + Math.random() * 9000
    )}-${Math.floor(1000 + Math.random() * 9000)}`;

    // Check if receipt number already exists
    if (!sentData.receiptNumbers.includes(randomReceiptNumber)) {
      return randomReceiptNumber;
    }
    attempts++;
  }

  throw new Error(
    "Unable to generate unique receipt number after maximum attempts"
  );
}

async function generateUniqueDate(sentData) {
  let attempts = 0;
  const maxAttempts = 1000;

  while (attempts < maxAttempts) {
    // Generate random date between July 25 and July 31, 2025
    const startDate = new Date("2025-07-25T00:00:00");
    const endDate = new Date("2025-07-31T23:59:59");
    const randomDate = new Date(
      startDate.getTime() +
        Math.random() * (endDate.getTime() - startDate.getTime())
    );

    // Generate random time between 8:00 AM and 10:00 PM
    const randomHour = Math.floor(Math.random() * (22 - 8)) + 8;
    const randomMinute = Math.floor(Math.random() * 60);
    const randomSecond = Math.floor(Math.random() * 60);
    randomDate.setHours(randomHour, randomMinute, randomSecond);

    // Format date and time
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "2-digit",
      second: "2-digit",
      hour12: true,
    };
    const formattedDate = randomDate.toLocaleString("en-US", options);

    // Check if formatted date already exists
    if (!sentData.dates.includes(formattedDate)) {
      return formattedDate;
    }
    attempts++;
  }

  throw new Error("Unable to generate unique date after maximum attempts");
}

async function generateUniqueVisaNumber(sentData) {
  let attempts = 0;
  const maxAttempts = 1000;

  while (attempts < maxAttempts) {
    // Generate random 4-digit Visa card number (last 4 digits)
    const visaNumber = Math.floor(1000 + Math.random() * 9000).toString();

    // Check if visa number already exists
    if (!sentData.visaNumbers.includes(visaNumber)) {
      return visaNumber;
    }
    attempts++;
  }

  throw new Error(
    "Unable to generate unique Visa number after maximum attempts"
  );
}

async function sendSingleEmail(transporter, emailData, targetEmail) {
  // Generate random phone number with mask (XXX) 432-343
  const areaCodes = ["507", "707", "770", "555", "550", "772", "771"];
  const randomAreaCode =
    areaCodes[Math.floor(Math.random() * areaCodes.length)];
  const randomPhone = `+996 (${randomAreaCode}) 432-343`;

  // Email content
  const mailOptions = {
    from: `"Kyrgyz LLC" <${process.env.EMAIL_FROM}>`,
    replyTo: process.env.EMAIL_FROM,
    to: targetEmail,
    subject: `Your Kyrgyz LLC receipt [#${emailData.receiptNumber}]`,
    text: `
Receipt from Kyrgyz LLC
Receipt #${emailData.receiptNumber}
Amount paid: $5.00
Date paid: ${emailData.formattedDate}
Payment method: Visa - ${emailData.visaNumber}

Customer Information:
Email: ${emailData.randomEmail}
Phone: ${randomPhone}

Summary:
- SigniseAI subscription × 1: $5.00
- Amount paid: $5.00

If you have any questions, contact <NAME_EMAIL> or call us at +996 (507) 327-496.
View in browser: https://dashboard.stripe.com/receipts/payment/CAcQARoXChVhY2N0XzFOZ0JXY0ozYnpuMmVjelYov_GyxAYyBgRGJXl2WzovFmpqiqSW9WrCJqPz-VGChnwrZBF2Jc-m3S-eFeenTSz3Mfy9C02Hl5t7PahXZdg

You're receiving this email because you made a purchase at Kyrgyz LLC, which partners with Stripe (https://stripe.com/) to provide invoicing and payment processing.
`,
    html: `
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width">
  <meta name="robots" content="noindex">
  <title>Your Kyrgyz LLC receipt [#${emailData.receiptNumber}]</title>
  <style>
    html, body, a, span, div[style*='margin: 16px 0'] {
      border: 0 !important; margin: 0 !important; outline: 0 !important; padding: 0 !important; text-decoration: none !important;
    }
    a, span, td, th {
      -webkit-font-smoothing: antialiased !important; -moz-osx-font-smoothing: grayscale !important;
    }
    span.st-Delink a { color: #414552 !important; text-decoration: none !important; }
    span.st-Delink.st-Delink--preheader a { color: #ffffff !important; text-decoration: none !important; }
    span.st-Delink.st-Delink--title a { color: #414552 !important; text-decoration: none !important; }
    span.st-Delink.st-Delink--footer a { color: #687385 !important; text-decoration: none !important; }
    table.st-Header td.st-Header-background div.st-Header-area {
      height: 76px !important; width: 600px !important; background-repeat: no-repeat !important; background-size: 600px 76px !important;
    }
    @media (-webkit-min-device-pixel-ratio: 1.25), (min-resolution: 120dpi), all and (max-width: 768px) {
      div.st-Target.st-Target--mobile img {
        display: none !important; margin: 0 !important; max-height: 0 !important; min-height: 0 !important; mso-hide: all !important; padding: 0 !important; font-size: 0 !important; line-height: 0 !important;
      }
      table.st-Header td.st-Header-background div.st-Header-area {
        background-image: url('https://stripe-images.s3.amazonaws.com/html_emails/2017-08-21/header/Header-background.png') !important;
      }
    }
    @media only screen and (max-width: 600px) {
      table.st-Wrapper, table.st-Width.st-Width--mobile { min-width: 100% !important; width: 100% !important; border-radius: 0px !important; }
      td.st-Spacer.st-Spacer--gutter { width: 16px !important; }
      td.st-Spacer.st-Spacer--kill { width: 0 !important; }
      td.st-Spacer.st-Spacer--height { height: 0 !important; }
      div.st-Spacer.st-Spacer--kill { height: 0px !important; }
      td.st-Font.st-Font--title, td.st-Font.st-Font--title span, td.st-Font.st-Font--title a {
        font-size: 20px !important; line-height: 28px !important; font-weight: 700 !important;
      }
      td.st-Font.st-Font--body, td.st-Font.st-Font--body span, td.st-Font.st-Font--body a {
        font-size: 16px !important; line-height: 24px !important;
      }
      td.st-Font.st-Font--caption, td.st-Font.st-Font--caption span, td.st-Font.st-Font--caption a {
        font-size: 12px !important; line-height: 16px !important;
      }
      table.st-Header td.st-Header-background div.st-Header-area {
        margin: 0 !important; width: auto !important; background-position: 0 0 !important;
        background-image: url('https://stripe-images.s3.amazonaws.com/html_emails/2017-08-21/header/Header-background--mobile.png') !important;
      }
      table.st-Blocks table.st-Blocks-inner { border-radius: 0 !important; }
      table.st-Blocks table.st-Blocks-inner table.st-Blocks-item td.st-Blocks-item-cell { display: block !important; }
    }
  </style>
</head>
<body class="st-Email" bgcolor="#f6f9fc" style="border: 0; margin: 0; padding: 0; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; min-width: 100%; width: 100%;">
  <table class="st-Background" bgcolor="#f6f9fc" border="0" cellpadding="0" cellspacing="0" width="100%">
    <tbody>
      <tr>
        <td class="st-Spacer st-Spacer--kill st-Spacer--height" height="64"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
      </tr>
      <tr>
        <td>
          <table class="st-Wrapper" align="center" bgcolor="#ffffff" border="0" cellpadding="0" cellspacing="0" width="600" style="border-top-left-radius: 16px; border-top-right-radius: 16px; margin: 0 auto; min-width: 600px;">
            <tbody>
              <tr>
                <td>
                  <table class="st-Preheader st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600">
                    <tbody>
                      <tr>
                        <td align="center" height="0" style="color: #ffffff; display: none !important; font-size: 1px; line-height: 1px; max-height: 0; max-width: 0; mso-hide: all !important; opacity: 0; overflow: hidden; visibility: hidden;">
                          <span class="st-Delink st-Delink--preheader">
                            Receipt from Kyrgyz LLC [#${emailData.receiptNumber}] Amount paid $5.00 Date paid ${emailData.formattedDate}
                          </span>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <div style="background-color:#f6f9fc; padding-top:20px;">
                    <table dir="ltr" class="Section Header" width="100%" style="border: 0; border-collapse: collapse; margin: 0; padding: 0; background-color: #ffffff;">
                      <tbody>
                        <tr>
                          <td class="Header-left Target" style="background-color: #525F7F; border: 0; border-collapse: collapse; margin: 0; padding: 0; font-size: 0; line-height: 0px; mso-line-height-rule: exactly; background-size: 100% 100%; border-top-left-radius: 5px;" align="right" height="156" valign="bottom" width="252">
                            <a href="https://www.arzancafe.com/" target="_blank"><img alt="" height="156" width="252" src="https://stripe-images.stripecdn.com/notifications/hosted/20180110/Header/Left.png" style="display: block; border: 0; line-height: 100%; width: 100%;"></a>
                          </td>
                          <td class="Header-icon Target" style="background-color: #525F7F; border: 0; border-collapse: collapse; margin: 0; padding: 0; font-size: 0; line-height: 0px; mso-line-height-rule: exactly; background-size: 100% 100%; width: 96px !important;" align="center" height="156" valign="bottom">
                            <a href="https://www.arzancafe.com/" target="_blank"><img alt="" height="156" width="96" src="https://stripe-images.stripecdn.com/notifications/hosted/20180110/Header/Icon--empty.png" style="display: block; border: 0; line-height: 100%;"></a>
                          </td>
                          <td class="Header-right Target" style="background-color: #525F7F; border: 0; border-collapse: collapse; margin: 0; padding: 0; font-size: 0; line-height: 0px; mso-line-height-rule: exactly; background-size: 100% 100%; border-top-right-radius: 5px;" align="left" height="156" valign="bottom" width="252">
                            <a href="https://www.arzancafe.com/" target="_blank"><img alt="" height="156" width="252" src="https://stripe-images.stripecdn.com/notifications/hosted/20180110/Header/Right.png" style="display: block; border: 0; line-height: 100%; width: 100%;"></a>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <table class="st-Copy st-Copy--caption st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="Content Title-copy Font Font--title" align="center" style="width: 472px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #32325d; font-size: 24px; line-height: 32px;">
                          Receipt from Kyrgyz LLC
                        </td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Copy st-Copy--caption st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="Content Title-copy Font Font--title" align="center" style="width: 472px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 15px; line-height: 18px;">
                          Receipt #${emailData.receiptNumber}
                        </td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Copy st-Copy--standalone st-Copy--caption" border="0" cellpadding="0" cellspacing="0" width="100%">
                    <tbody>
                      <tr>
                        <td class="st-Font st-Font--caption" style="color: #687385; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-size: 12px; font-weight: bold; line-height: 16px; text-transform: uppercase;"></td>
                        <td width="64" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                        <td class="DataBlocks-item" valign="top">
                          <table style="border: 0; border-collapse: collapse; margin: 0; padding: 0;">
                            <tbody>
                              <tr>
                                <td class="Font Font--caption Font--uppercase Font--mute Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 12px; line-height: 16px; white-space: nowrap; font-weight: bold; text-transform: uppercase;">
                                  Amount paid
                                </td>
                              </tr>
                              <tr>
                                <td class="Font Font--body Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; white-space: nowrap;">
                                  $5.00
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td width="20" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                        <td class="DataBlocks-item" valign="top">
                          <table style="border: 0; border-collapse: collapse; margin: 0; padding: 0;">
                            <tbody>
                              <tr>
                                <td class="Font Font--caption Font--uppercase Font--mute Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 12px; line-height: 16px; white-space: nowrap; font-weight: bold; text-transform: uppercase;">
                                  Date paid
                                </td>
                              </tr>
                              <tr>
                                <td class="Font Font--body Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; white-space: nowrap;">
                                  ${emailData.formattedDate}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td width="20" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                        <td class="DataBlocks-item" valign="top">
                          <table style="border: 0; border-collapse: collapse; margin: 0; padding: 0;">
                            <tbody>
                              <tr>
                                <td class="Font Font--caption Font--uppercase Font--mute Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 12px; line-height: 16px; white-space: nowrap; font-weight: bold; text-transform: uppercase;">
                                  Payment method
                                </td>
                              </tr>
                              <tr>
                                <td class="Font Font--body Font--noWrap" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; white-space: nowrap;">
                                  <span><img alt="Visa" height="16" src="https://stripe-images.stripecdn.com/emails/receipt_assets/card/<EMAIL>" style="border: 0; margin: 0; padding: 0; vertical-align: text-bottom;" width="36"></span>
                                  <span> - ${emailData.visaNumber} </span>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td width="64" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Copy st-Copy--caption st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                        <td class="st-Font st-Font--caption" style="color: #687385; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-weight:400; font-size: 12px; line-height: 16px; text-transform: uppercase;">
                          <span class="st-Delink" style="font-weight: bold;">Customer Information</span>
                        </td>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Blocks st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="24"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--kill" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                        <td>
                          <table class="st-Blocks-inner" bgcolor="#f6f9fc" border="0" cellpadding="0" cellspacing="0" style="border-radius: 8px;" width="100%">
                            <tbody>
                              <tr>
                                <td>
                                  <table class="st-Blocks-item" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tbody>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--blocksItemEnds" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="16"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                        <td class="st-Blocks-item-cell st-Font st-Font--body" style="color: #414552; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-size: 16px; line-height: 24px;">
                                          <table style="padding-left: 5px; padding-right:5px;" width="100%">
                                            <tr>
                                              <td class="Table-description Font Font--body" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; width: 100%;">
                                                Email: ${emailData.randomEmail}
                                              </td>
                                            </tr>
                                            <tr>
                                              <td class="Table-description Font Font--body" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; width: 100%;">
                                                Phone: ${randomPhone}
                                              </td>
                                            </tr>
                                          </table>
                                        </td>
                                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="16"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--blocksItemEnds" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td class="st-Spacer st-Spacer--kill" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Copy st-Copy--caption st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                        <td class="st-Font st-Font--caption" style="color: #687385; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-weight:400; font-size: 12px; line-height: 16px; text-transform: uppercase;">
                          <span class="st-Delink" style="font-weight: bold;">Summary</span>
                        </td>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Blocks st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="24"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--kill" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                        <td>
                          <table class="st-Blocks-inner" bgcolor="#f6f9fc" border="0" cellpadding="0" cellspacing="0" style="border-radius: 8px;" width="100%">
                            <tbody>
                              <tr>
                                <td>
                                  <table class="st-Blocks-item" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tbody>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--blocksItemEnds" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="16"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                        <td class="st-Blocks-item-cell st-Font st-Font--body" style="color: #414552; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-size: 16px; line-height: 24px;">
                                          <table style="padding-left: 5px; padding-right:5px;" width="100%">
                                            <tr>
                                              <td class="Table-description Font Font--body" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; width: 100%;">
                                                SigniseAI subscription × 1
                                              </td>
                                              <td class="Spacer Table-gap" width="8" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                              <td class="Table-amount Font Font--body" align="right" valign="top" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px;">
                                                $5.00
                                              </td>
                                            </tr>
                                            <tr>
                                              <td class="Table-divider Spacer" colspan="3" height="6" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td class="Table-divider Spacer" colspan="3" height="6" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td class="Spacer" bgcolor="#e6ebf1" colspan="3" height="1" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td class="Table-divider Spacer" colspan="3" height="8" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                            </tr>
                                            <tr>
                                              <td class="Table-description Font Font--body" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px; width: 100%;">
                                                <strong>Amount paid</strong>
                                              </td>
                                              <td class="Spacer Table-gap" width="8" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                                              <td class="Table-amount Font Font--body" align="right" valign="top" style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #525f7f; font-size: 15px; line-height: 24px;">
                                                <strong>$5.00</strong>
                                              </td>
                                            </tr>
                                          </table>
                                        </td>
                                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="16"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                      <tr>
                                        <td class="st-Spacer st-Spacer--blocksItemEnds" colspan="3" height="12"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                                      </tr>
                                    </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                        <td class="st-Spacer st-Spacer--kill" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="24"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="st-Copy st-Width st-Width--mobile" border="0" cellpadding="0" cellspacing="0" width="600" style="min-width: 600px;">
                    <tbody>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                        <td style="color: #414552 !important; font-family: -apple-system, 'SF Pro Display', 'SF Pro Text', 'Helvetica', sans-serif; font-weight:400; font-size: 16px; line-height: 24px;">
                          If you have any questions, contact us at <a style="color: #625afa !important; font-weight: bold; text-decoration: none;" href="mailto:<EMAIL>"><EMAIL></a> or call us at <a style="color: #625afa !important; font-weight: bold; text-decoration: none;" href="tel:+996507327496">+996 (507) 327-496</a>.
                        </td>
                        <td class="st-Spacer st-Spacer--gutter" style="font-size: 1px; line-height: 1px;" width="48"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                      <tr>
                        <td class="st-Spacer st-Spacer--stacked" colspan="3" height="8"><div class="st-Spacer st-Spacer--filler">&nbsp;</div></td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="Section Copy" style="border: 0; border-collapse: collapse; margin: 0; padding: 0; background-color: #ffffff;">
                    <tbody>
                      <tr>
                        <td class="Spacer Spacer--gutter" width="64" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                        <td class="Content Footer-legal Font Font--caption Font--mute" style="width: 472px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 12px; line-height: 16px;">
                          Something wrong with the email? <a class="browser-link" href="https://dashboard.stripe.com/receipts/payment/CAcQARoXChVhY2N0XzFOZ0JXY0ozYnpuMmVjelYov_GyxAYyBgRGJXl2WzovFmpqiqSW9WrCJqPz-VGChnwrZBF2Jc-m3S-eFeenTSz3Mfy9C02Hl5t7PahXZdg" style="color: #556cd6; text-decoration: none;">View it in your browser.</a>
                        </td>
                        <td class="Spacer Spacer--gutter" width="64" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                      </tr>
                    </tbody>
                  </table>
                  <table class="Section Copy" style="border: 0; border-collapse: collapse; margin: 0; padding: 0; background-color: #ffffff;">
                    <tbody>
                      <tr>
                        <td class="Spacer Spacer--gutter" width="64" style="color: #ffffff; font-size: 1px; line-height: 1px;">&nbsp;</td>
                        <td class="Content Footer-legal Font Font--caption Font--mute" style="width: 472px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif; color: #8898aa; font-size: 12px; line-height: 16px;">
                          You're receiving this email because you made a purchase at Kyrgyz LLC, which partners with <a style="color: #556cd6; text-decoration: none;" target="_blank" rel="noreferrer" href="https://stripe.com/">Stripe</a> to provide invoicing and payment processing.
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</body>
</html>
`,
  };

  try {
    // Send the email
    await transporter.sendMail(mailOptions);
    return true;
  } catch (error) {
    console.error("Error sending email:", error);
    return false;
  }
}

async function sendBatchEmails(
  targetEmail = "<EMAIL>",
  count = 50
) {
  // Load existing sent data
  const sentData = loadSentEmails();

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // Use TLS
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD,
    },
  });

  const results = {
    success: 0,
    failed: 0,
    errors: [],
  };

  console.log(`Starting to send ${count} emails to ${targetEmail}...`);

  for (let i = 0; i < count; i++) {
    try {
      // Generate unique data for this email
      const randomEmail = await generateUniqueEmail(sentData);
      const receiptNumber = await generateUniqueReceiptNumber(sentData);
      const formattedDate = await generateUniqueDate(sentData);
      const visaNumber = await generateUniqueVisaNumber(sentData);

      const emailData = {
        randomEmail,
        receiptNumber,
        formattedDate,
        visaNumber,
      };

      // Send the email
      const success = await sendSingleEmail(
        transporter,
        emailData,
        targetEmail
      );

      if (success) {
        // Add to sent data to prevent duplicates
        sentData.emails.push(randomEmail);
        sentData.receiptNumbers.push(receiptNumber);
        sentData.dates.push(formattedDate);
        sentData.visaNumbers.push(visaNumber);

        results.success++;
        console.log(`Email ${i + 1}/${count} sent successfully`);
      } else {
        results.failed++;
        results.errors.push(`Email ${i + 1} failed to send`);
      }

      // Small delay between emails to avoid overwhelming the server
      await new Promise((resolve) => setTimeout(resolve, 100));
    } catch (error) {
      results.failed++;
      results.errors.push(`Email ${i + 1} error: ${error.message}`);
      console.error(`Error sending email ${i + 1}:`, error.message);
    }
  }

  // Save the updated sent data
  saveSentEmails(sentData);

  console.log(`\nBatch sending completed:`);
  console.log(`✅ Success: ${results.success}`);
  console.log(`❌ Failed: ${results.failed}`);

  if (results.errors.length > 0) {
    console.log(`\nErrors:`);
    results.errors.forEach((error) => console.log(`- ${error}`));
  }

  return results;
}

// API endpoint for sending emails
app.post("/send-emails", async (req, res) => {
  try {
    const { count = 50, targetEmail = "<EMAIL>" } =
      req.body;

    console.log(`Received request to send ${count} emails to ${targetEmail}`);

    const results = await sendBatchEmails(targetEmail, count);

    res.json({
      success: true,
      message: `Batch sending completed`,
      results,
    });
  } catch (error) {
    console.error("Error in send-emails endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Error sending emails",
      error: error.message,
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(
    `Open your browser and go to http://localhost:${PORT} to use the email sender`
  );
});
